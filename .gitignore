# Dependencies
node_modules/
.pnpm-debug.log*

# Session files
cookies.txt

# Frontend build outputs
frontend/dist/
frontend/build/
frontend/.next/
frontend/.nuxt/
frontend/.vite/

# Backend build outputs
packages/riva_ash/_build/
packages/riva_ash/deps/
packages/riva_ash/cover/
packages/riva_ash/doc/
packages/riva_ash/hex/
packages/riva_ash/mix/

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Database
packages/riva_ash/priv/static/

# Logs
*.log
logs/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE files
.vscode/
.idea/
*.swp
*.swo

# Temporary files
*.tmp
*.temp

# Elixir specific
packages/riva_ash/erl_crash.dump
packages/riva_ash/.elixir_ls/

# Mix artifacts
packages/riva_ash/mix.lock.backup
