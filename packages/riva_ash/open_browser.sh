#!/bin/bash

echo "🚀 Opening browser with Phoenix app..."

# Start Phoenix server in background
echo "📡 Starting Phoenix server..."
MIX_ENV=test mix phx.server &
SERVER_PID=$!

echo "⏳ Waiting for server to start..."
sleep 5

# Test if server is responding
echo "🔍 Testing server connection..."
if curl -s http://localhost:4002/ > /dev/null; then
    echo "✅ Server is running!"
else
    echo "❌ Server not responding"
    kill $SERVER_PID 2>/dev/null
    exit 1
fi

echo "🌐 Opening browser to registration page..."
chromium --new-window --no-first-run --disable-default-apps "http://localhost:4002/register" &
BROWSER_PID=$!

echo "Browser opened with PID: $BROWSER_PID"
echo "You should now see the Phoenix app registration page!"
echo ""
echo "Press Enter to run Phoenix Test + Playwright automation..."
read

echo "🤖 Running Phoenix Test + Playwright..."
PLAYWRIGHT_HEADLESS=false mix test test/riva_ash_web/authentication_playwright_test.exs:25 --max-failures 1

echo ""
echo "🧹 Cleaning up..."
kill $BROWSER_PID 2>/dev/null
kill $SERVER_PID 2>/dev/null

echo "✅ Done!"
