const { chromium } = require('playwright');

(async () => {
  console.log('🚀 Starting direct Playwright test...');
  
  // Launch browser with visible window
  const browser = await chromium.launch({ 
    headless: false,
    slowMo: 2000  // Slow down actions
  });
  
  console.log('🌐 Browser launched!');
  
  const page = await browser.newPage();
  
  console.log('📄 Navigating to Phoenix app...');
  await page.goto('http://localhost:4002/register');
  
  console.log('📝 Filling registration form...');
  await page.fill('input[name="name"]', 'Playwright Test User');
  await page.fill('input[name="email"]', '<EMAIL>');
  await page.fill('input[name="password"]', 'password123');
  await page.fill('input[name="password_confirmation"]', 'password123');
  
  console.log('🖱️ Clicking submit button...');
  await page.click('button[type="submit"]');
  
  console.log('⏳ Waiting for redirect...');
  await page.waitForURL('**/sign-in');
  
  console.log('✅ Test completed successfully!');
  console.log('📸 Taking screenshot...');
  await page.screenshot({ path: 'tmp/direct_playwright_test.png' });
  
  console.log('🧹 Closing browser...');
  await browser.close();
  
  console.log('🎉 Direct Playwright test finished!');
})().catch(error => {
  console.error('❌ Error:', error);
  process.exit(1);
});
