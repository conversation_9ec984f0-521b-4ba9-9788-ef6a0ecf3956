#!/usr/bin/env elixir

# Start the application
{:ok, _} = Application.ensure_all_started(:riva_ash)

IO.puts("🔍 DEBUG: Phoenix Test Playwright Debug Script")
IO.puts("============================================")

# Check if PhoenixTest.Playwright.Case is available
case Code.ensure_loaded(PhoenixTest.Playwright.Case) do
  {:module, _} -> 
    IO.puts("✅ PhoenixTest.Playwright.Case is loaded")
  {:error, reason} -> 
    IO.puts("❌ PhoenixTest.Playwright.Case failed to load: #{reason}")
    System.halt(1)
end

# Check configuration
config = Application.get_env(:phoenix_test, :playwright, [])
IO.puts("📋 Phoenix Test Playwright config: #{inspect(config)}")

headless = Keyword.get(config, :headless, true)
IO.puts("🌐 Headless mode: #{headless}")

# Try to create a simple test
try do
  IO.puts("🧪 Creating test module...")
  
  defmodule DebugTest do
    use PhoenixTest.Playwright.Case,
      async: false,
      headless: false
    
    import PhoenixTest
    
    def run_browser_test do
      IO.puts("🚀 Starting browser test...")
      
      conn = Phoenix.ConnTest.build_conn()
      IO.puts("📡 Connection created")
      
      result = conn
      |> visit("/register")
      |> assert_has("h2", text: "Create a new account")
      
      IO.puts("✅ Browser test completed!")
      result
    end
  end
  
  IO.puts("🎭 Running browser test...")
  DebugTest.run_browser_test()
  IO.puts("🎉 SUCCESS: Phoenix Test Playwright is working!")
  
rescue
  error ->
    IO.puts("❌ ERROR: #{inspect(error)}")
    IO.puts("📋 Stacktrace:")
    IO.puts(Exception.format_stacktrace(__STACKTRACE__))
end
