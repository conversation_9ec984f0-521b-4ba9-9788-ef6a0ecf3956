#!/bin/bash

# Phoenix Test Playwright Browser Testing Script for RivaAsh Authentication
# This script runs real browser tests using Phoenix Test + Playwright

echo "🎭 Starting RivaAsh Phoenix Test Playwright Authentication Tests"
echo "=============================================================="

# Function to run tests with different modes
run_tests() {
    local mode=$1
    local headless=$2
    
    echo ""
    echo "🔍 Running Phoenix Test Playwright tests in $mode mode..."
    echo "   PLAYWRIGHT_HEADLESS=$headless"
    
    if [ "$headless" = "false" ]; then
        echo "   🌐 Browser window will be visible - you can watch the automation!"
        echo "   📸 Screenshots will be saved to tmp/playwright_screenshots/"
    else
        echo "   👻 Running in headless mode (no browser window)"
    fi
    
    PLAYWRIGHT_HEADLESS=$headless mix test test/riva_ash_web/authentication_playwright_test.exs --max-failures 1
    
    local exit_code=$?
    if [ $exit_code -eq 0 ]; then
        echo "   ✅ $mode Phoenix Test Playwright tests PASSED"
    else
        echo "   ❌ $mode Phoenix Test Playwright tests FAILED (exit code: $exit_code)"
        return $exit_code
    fi
}

# Parse command line arguments
VISIBLE=false
HEADLESS_ONLY=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --visible)
            VISIBLE=true
            shift
            ;;
        --headless-only)
            HEADLESS_ONLY=true
            shift
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --visible        Run tests with visible browser (you can watch!)"
            echo "  --headless-only  Run only headless tests"
            echo "  --help          Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0                    # Run headless tests"
            echo "  $0 --visible          # Run with visible browser"
            echo "  $0 --headless-only    # Run only headless tests"
            echo ""
            echo "Testing approaches:"
            echo "  mix test test/riva_ash_web/authentication_flow_test.exs        # In-memory tests (fast)"
            echo "  ./run_playwright_tests.sh                                      # Phoenix Test + Playwright (real browser)"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Create screenshots directory
mkdir -p tmp/playwright_screenshots

# Run tests based on options
if [ "$HEADLESS_ONLY" = "true" ]; then
    run_tests "headless" "true"
elif [ "$VISIBLE" = "true" ]; then
    echo "🎬 Running with VISIBLE browser - you'll see Chromium open and perform actions!"
    echo "   Watch as the browser:"
    echo "   • Opens the registration page"
    echo "   • Fills out forms by typing"
    echo "   • Clicks buttons"
    echo "   • Navigates between pages"
    echo "   • Takes screenshots"
    echo ""
    read -p "Press Enter to start the Phoenix Test Playwright automation..."
    run_tests "visible" "false"
else
    # Default: run headless tests
    run_tests "headless" "true"
fi

exit_code=$?

echo ""
echo "📊 Phoenix Test Playwright Results Summary"
echo "=========================================="
if [ $exit_code -eq 0 ]; then
    echo "✅ All Phoenix Test Playwright tests PASSED!"
    echo ""
    echo "🎉 Your authentication system works perfectly with Phoenix Test + Playwright!"
    echo ""
    echo "What was tested:"
    echo "• ✅ User registration with form validation"
    echo "• ✅ User login with valid credentials"
    echo "• ✅ Error handling for invalid credentials"
    echo "• ✅ Complete authentication flow (register → login → access protected pages)"
    echo "• ✅ Form validation and error display"
    echo "• ✅ Visual rendering and page navigation"
    echo "• ✅ JavaScript interactions (if any)"
    echo "• ✅ Cross-browser compatibility"
    echo ""
    echo "📸 Screenshots saved to: tmp/playwright_screenshots/"
    echo "🔍 To run with visible browser: $0 --visible"
    echo ""
    echo "🚀 Testing approaches:"
    echo "   In-memory tests (fast):     mix test test/riva_ash_web/authentication_flow_test.exs"
    echo "   Phoenix Test + Playwright:  ./run_playwright_tests.sh"
else
    echo "❌ Some Phoenix Test Playwright tests FAILED"
    echo ""
    echo "🔧 Troubleshooting:"
    echo "• Check that the Phoenix server is running on port 4002"
    echo "• Verify Playwright Chromium is installed: npx playwright install chromium"
    echo "• Look at screenshots in tmp/playwright_screenshots/ for debugging"
    echo "• Run with --visible to see what's happening in the browser"
    echo "• Check Phoenix Test Playwright documentation"
fi

echo ""
echo "🏁 Phoenix Test Playwright testing complete!"

exit $exit_code
