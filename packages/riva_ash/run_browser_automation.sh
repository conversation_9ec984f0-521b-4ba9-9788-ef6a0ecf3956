#!/bin/bash

echo "🎭 Phoenix Test + Playwright Browser Automation"
echo "=============================================="

# Kill any existing processes
pkill -f "mix phx.server" 2>/dev/null || true
pkill -f "chromium" 2>/dev/null || true

echo "📡 Starting Phoenix server..."
MIX_ENV=test mix phx.server &
SERVER_PID=$!

echo "⏳ Waiting for server to start..."
sleep 5

# Test server
if curl -s http://localhost:4002/ > /dev/null; then
    echo "✅ Phoenix server is running on http://localhost:4002"
else
    echo "❌ Phoenix server failed to start"
    kill $SERVER_PID 2>/dev/null
    exit 1
fi

echo ""
echo "🌐 Opening browser to show the registration page..."
chromium --new-window "http://localhost:4002/register" &
BROWSER_PID=$!

echo "Browser opened! You should see the registration page."
echo ""
echo "Now I'll run Phoenix Test + Playwright to automate the browser..."
echo "Press Enter to start the automation..."
read

echo ""
echo "🤖 Running Phoenix Test + Playwright automation..."
echo "Watch the browser - it should automatically fill the form and submit it!"

# Force visible browser and run the test
PLAYWRIGHT_HEADLESS=false mix test test/riva_ash_web/authentication_playwright_test.exs:25 --max-failures 1 --seed 0

TEST_RESULT=$?

echo ""
if [ $TEST_RESULT -eq 0 ]; then
    echo "✅ Phoenix Test + Playwright automation completed successfully!"
    echo "The browser should have automatically:"
    echo "  • Filled in the Name field"
    echo "  • Filled in the Email field" 
    echo "  • Filled in the Password fields"
    echo "  • Clicked the Create Account button"
    echo "  • Navigated to the sign-in page"
else
    echo "❌ Phoenix Test + Playwright automation failed"
fi

echo ""
echo "🧹 Cleaning up..."
kill $BROWSER_PID 2>/dev/null || true
kill $SERVER_PID 2>/dev/null || true

echo "✅ Done!"
