defmodule SimpleBrowserTest do
  use PhoenixTest.Playwright.Case,
    async: false,
    headless: false

  import PhoenixTest
  alias RivaAsh.Accounts.User

  test "browser should open and perform automation", %{conn: conn} do
    conn
    |> visit("/register")
    |> assert_has("h2", text: "Create a new account")
    |> fill_in("Name", with: "Browser Test User")
    |> fill_in("Email", with: "<EMAIL>")
    |> fill_in("Password", with: "password123")
    |> fill_in("Confirm Password", with: "password123")
    |> click_button("Create Account")
    |> assert_path("/sign-in")
    |> assert_has("h2", text: "Sign in to your account")

    # Verify user was created
    query = User |> Ash.Query.for_read(:read) |> Ash.Query.filter(email == "<EMAIL>")
    assert {:ok, [user]} = Ash.read(query, domain: RivaAsh.Accounts)
    assert user.name == "Browser Test User"
  end
end
